import React from "react";

export default function page() {
  return (
    <div className="min-h-screen overflow-hidden" style={{ fontFamily: 'General Sans, sans-serif' }}>
      <div className="flex h-screen">
        {/* Left Side - White Background */}
        <div className="flex-1 bg-white flex flex-col">
          {/* Header */}
          <div className="px-8 py-8">
            <h1 className="text-2xl font-bold text-blue-600">DRIPLY</h1>
          </div>

          {/* Left Content */}
          <div className="flex-1 flex items-center px-8">
            <div className="max-w-xl space-y-8">
              <div className="space-y-6">
                <h2 className="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                  Links Don't Sell.
                  <br />
                  <span className="text-gray-900">Smart Conversations Do</span>
                </h2>

                <p className="text-gray-600 text-lg leading-relaxed max-w-md">
                  Driply turns your Instagram bio link into an AI assistant that
                  converts followers into customers. Get personalized
                  recommendations that drive real results for your business.
                </p>
              </div>

              <button className="bg-[#DCFB41] text-black font-semibold px-8 py-4 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl">
                Learn More
              </button>
            </div>
          </div>
        </div>

        {/* Right Side - Gray Background */}
        <div className="flex-1 bg-gray-100 relative overflow-hidden">
          <div className="relative flex items-center justify-center h-full">
            {/* Instagram Profile Phone (Behind/Left) */}
            <div className="absolute left-8 top-12 z-10 bg-black rounded-[2.5rem] p-2 shadow-2xl transform -rotate-3">
              <div className="bg-black rounded-[2rem] overflow-hidden w-64 h-[520px]">
                {/* Status Bar */}
                <div className="text-white px-4 py-2 text-sm flex justify-between items-center">
                  <span>9:41</span>
                  <div className="flex gap-1 items-center">
                    <div className="w-4 h-2 bg-white rounded-sm"></div>
                    <div className="w-1 h-2 bg-white rounded-sm"></div>
                    <div className="w-6 h-2 bg-white rounded-sm"></div>
                  </div>
                </div>

                {/* Instagram Profile */}
                <div className="p-4 space-y-4">
                  {/* Header */}
                  <div className="flex items-center gap-3 text-white">
                    <button className="text-lg">←</button>
                    <h3 className="font-semibold">The.Curly.Yogini</h3>
                    <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center ml-1">
                      <span className="text-white text-xs">✓</span>
                    </div>
                  </div>

                  {/* Profile Section */}
                  <div className="flex items-center gap-4">
                    <div className="relative">
                      <div className="w-20 h-20 rounded-full overflow-hidden border-4 border-gradient-to-r from-pink-500 to-yellow-500 p-1">
                        <div className="w-full h-full bg-gradient-to-br from-amber-400 to-orange-600 rounded-full flex items-center justify-center">
                          <span className="text-white text-lg font-bold">
                            CY
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex-1">
                      <div className="flex justify-between text-center text-white">
                        <div>
                          <div className="font-bold">657</div>
                          <div className="text-xs text-gray-300">Posts</div>
                        </div>
                        <div>
                          <div className="font-bold">10.2K</div>
                          <div className="text-xs text-gray-300">
                            Followers
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Name */}
                  <div className="text-white">
                    <div className="font-semibold">The.Curly.Yogini</div>
                  </div>

                  {/* Bio Link */}
                  <div className="bg-gray-800 rounded-full px-4 py-2 text-center">
                    <span className="text-blue-400 text-sm">
                      driply.chat/thecurlyyogini
                    </span>
                  </div>

                  {/* Follow Button */}
                  <button className="w-full bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2.5 rounded-lg transition-colors">
                    Follow
                  </button>
                </div>
              </div>
            </div>

            {/* Chat Interface Phone (Front/Right) */}
            <div className="absolute right-8 top-0 z-20 bg-black rounded-[2.5rem] p-2 shadow-2xl transform rotate-2">
              <div className="bg-gradient-to-b from-purple-200 to-purple-300 rounded-[2rem] overflow-hidden w-72 h-[560px]">
                {/* Status Bar */}
                <div className="text-black px-4 py-2 text-sm flex justify-between items-center">
                  <span>9:41</span>
                  <div className="flex gap-1 items-center">
                    <div className="w-4 h-2 bg-black rounded-sm"></div>
                    <div className="w-1 h-2 bg-black rounded-sm"></div>
                    <div className="w-6 h-2 bg-black rounded-sm"></div>
                  </div>
                </div>

                {/* Chat Content */}
                <div className="p-6 space-y-6 h-full flex flex-col">
                  {/* Profile Avatar */}
                  <div className="flex items-center justify-center">
                    <div className="w-16 h-16 rounded-full overflow-hidden">
                      <div className="w-full h-full bg-gradient-to-br from-amber-400 to-orange-600 rounded-full flex items-center justify-center">
                        <span className="text-white text-lg font-bold">
                          CY
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Profile Name */}
                  <div className="text-center">
                    <h3 className="font-semibold text-gray-800">
                      The Curly Yogini
                    </h3>
                  </div>

                  {/* Main Message */}
                  <div className="text-center space-y-2">
                    <h2 className="text-xl font-bold text-gray-800">
                      Let's start stretching!
                    </h2>
                    <p className="text-gray-600 text-sm">Lessons? Times?</p>
                    <p className="text-gray-600 text-sm">Or anything else...</p>
                  </div>

                  {/* Quick Action Cards */}
                  <div className="space-y-3 flex-1">
                    <div className="flex gap-2">
                      <div className="flex-1 bg-white/70 rounded-2xl p-3">
                        <div className="font-semibold text-gray-800 text-sm">
                          Lessons
                        </div>
                        <div className="text-xs text-gray-600">
                          All lessons type
                        </div>
                      </div>
                      <div className="flex-1 bg-white/70 rounded-2xl p-3">
                        <div className="font-semibold text-gray-800 text-sm">
                          Morning routine course
                        </div>
                        <div className="text-xs text-gray-600">
                          10% off until today!
                        </div>
                      </div>
                    </div>

                    {/* Input Box */}
                    <div className="bg-white/70 rounded-2xl p-4 mt-auto">
                      <div className="text-gray-600 text-sm mb-2">
                        Ask anything
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex-1"></div>
                        <div className="w-8 h-8 bg-black rounded-full flex items-center justify-center">
                          <span className="text-white text-sm">→</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
